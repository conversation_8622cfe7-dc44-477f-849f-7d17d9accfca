@echo off
echo Testing batch extract script...
echo Current directory: %CD%
echo.

:: Test WinRAR path
echo Checking WinRAR installation...
if exist "D:\Program Files (x86)\WinRAR\WinRAR.exe" (
    echo SUCCESS: WinRAR found at D:\Program Files (x86)\WinRAR\WinRAR.exe
    set winrar="D:\Program Files (x86)\WinRAR\WinRAR.exe"
) else (
    echo ERROR: WinRAR not found at D:\Program Files (x86)\WinRAR\WinRAR.exe
    set winrar=
)

echo.
echo Checking for ZIP files in current directory...
dir *.zip /b 2>nul
if %errorlevel%==0 (
    echo ZIP files found
) else (
    echo No ZIP files found
)

echo.
echo Checking for RAR files in current directory...
dir *.rar /b 2>nul
if %errorlevel%==0 (
    echo RAR files found
) else (
    echo No RAR files found
)

echo.
echo Test completed. Press any key to exit...
pause >nul
