@echo off
setlocal enabledelayedexpansion

echo ========================================
echo           Batch Extract Tool
echo ========================================
echo.
echo Current directory: %CD%
echo.

:: 设置支持的压缩文件格式
set "extensions=*.zip *.rar *.7z *.tar *.gz *.bz2"

:: 检查解压软件
set "winrar="
set "sevenzip="

:: 首先检查用户指定的WinRAR路径
echo Checking for WinRAR...
if exist "D:\Program Files (x86)\WinRAR\WinRAR.exe" (
    set winrar="D:\Program Files (x86)\WinRAR\WinRAR.exe"
    echo Found WinRAR at: D:\Program Files (x86)\WinRAR\WinRAR.exe
) else (
    echo WinRAR not found at D:\Program Files (x86)\WinRAR\WinRAR.exe
    :: 检查常见的WinRAR安装路径
    if exist "%ProgramFiles%\WinRAR\WinRAR.exe" (
        set winrar="%ProgramFiles%\WinRAR\WinRAR.exe"
        echo Found WinRAR at: %ProgramFiles%\WinRAR\WinRAR.exe
    ) else if exist "%ProgramFiles(x86)%\WinRAR\WinRAR.exe" (
        set winrar="%ProgramFiles(x86)%\WinRAR\WinRAR.exe"
        echo Found WinRAR at: %ProgramFiles(x86)%\WinRAR\WinRAR.exe
    ) else (
        echo WinRAR not found in common locations
    )
)

:: 检查7-Zip作为备选
if exist "%ProgramFiles%\7-Zip\7z.exe" (
    set sevenzip="%ProgramFiles%\7-Zip\7z.exe"
) else if exist "%ProgramFiles(x86)%\7-Zip\7z.exe" (
    set sevenzip="%ProgramFiles(x86)%\7-Zip\7z.exe"
)

:: 显示找到的解压软件
if not "%winrar%"=="" (
    echo Using extraction software: WinRAR
) else if not "%sevenzip%"=="" (
    echo Using extraction software: 7-Zip
) else (
    echo Warning: WinRAR or 7-Zip not found, only ZIP files will be supported
)
echo.

:: 计数器
set /a total_files=0
set /a success_count=0
set /a error_count=0

echo Starting to scan archive files...
echo.

:: 遍历当前目录及所有子目录中的压缩文件
for /r %%i in (%extensions%) do (
    if exist "%%i" (
        set /a total_files+=1
        echo Processing: %%~nxi

        :: 获取文件路径和名称（不含扩展名）
        set filepath=%%~dpi
        set filename=%%~ni
        set fullpath=%%i

        :: 创建解压目录（以文件名命名）
        set extract_dir=!filepath!!filename!

        :: 如果目录已存在，询问是否覆盖
        if exist "!extract_dir!" (
            echo   Directory "!filename!" already exists
            set /p choice="  Overwrite? (Y/N): "
            if /i "!choice!" neq "Y" (
                echo   Skipping file: %%~nxi
                echo.
                goto :continue
            )
            rd /s /q "!extract_dir!" 2>nul
        )
        
        :: 创建解压目录
        mkdir "!extract_dir!" 2>nul

        :: 根据文件扩展名选择解压方法
        set file_ext=%%~xi
        set extract_success=0

        if /i "!file_ext!"==".zip" (
            if not "%winrar%"=="" (
                call :extract_winrar "!fullpath!" "!extract_dir!"
            ) else (
                call :extract_zip "!fullpath!" "!extract_dir!"
            )
        ) else if not "%winrar%"=="" (
            call :extract_winrar "!fullpath!" "!extract_dir!"
        ) else if not "%sevenzip%"=="" (
            call :extract_7zip "!fullpath!" "!extract_dir!"
        ) else (
            echo   Error: Unsupported file format !file_ext! and no extraction software found
            set /a error_count+=1
            goto :continue
        )

        if !extract_success!==1 (
            echo   Success: !filename!
            set /a success_count+=1
        ) else (
            echo   Failed: !filename!
            set /a error_count+=1
            :: 删除可能创建的空目录
            rd "!extract_dir!" 2>nul
        )
        
        :continue
        echo.
    )
)

:: 显示统计结果
echo ========================================
echo        Extraction Complete
echo ========================================
echo Total files: %total_files%
echo Successfully extracted: %success_count%
echo Failed to extract: %error_count%
echo ========================================

if %total_files%==0 (
    echo No archive files found!
)

echo.
echo Press any key to exit...
pause >nul
goto :eof

:: ZIP文件解压函数（使用PowerShell）
:extract_zip
set zip_file=%~1
set dest_dir=%~2

powershell -command "try { Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%zip_file%', '%dest_dir%'); exit 0 } catch { exit 1 }" >nul 2>&1

if %errorlevel%==0 (
    set extract_success=1
) else (
    set extract_success=0
)
goto :eof

:: WinRAR解压函数
:extract_winrar
set archive_file=%~1
set dest_dir=%~2

%winrar% x "%archive_file%" "%dest_dir%\" -y >nul 2>&1

if %errorlevel%==0 (
    set extract_success=1
) else (
    set extract_success=0
)
goto :eof

:: 7-Zip解压函数
:extract_7zip
set archive_file=%~1
set dest_dir=%~2

%sevenzip% x "%archive_file%" -o"%dest_dir%" -y >nul 2>&1

if %errorlevel%==0 (
    set extract_success=1
) else (
    set extract_success=0
)
goto :eof
