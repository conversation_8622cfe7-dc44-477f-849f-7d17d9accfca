@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo           批量解压工具
echo ========================================
echo.

:: 设置支持的压缩文件格式
set "extensions=*.zip *.rar *.7z *.tar *.gz *.bz2"

:: 检查解压软件
set "winrar="
set "sevenzip="

:: 首先检查用户指定的WinRAR路径
if exist "D:\Program Files (x86)\WinRAR\WinRAR.exe" (
    set "winrar=D:\Program Files (x86)\WinRAR\WinRAR.exe"
    echo 找到WinRAR: !winrar!
) else (
    :: 检查常见的WinRAR安装路径
    if exist "%ProgramFiles%\WinRAR\WinRAR.exe" (
        set "winrar=%ProgramFiles%\WinRAR\WinRAR.exe"
    ) else if exist "%ProgramFiles(x86)%\WinRAR\WinRAR.exe" (
        set "winrar=%ProgramFiles(x86)%\WinRAR\WinRAR.exe"
    )
)

:: 检查7-Zip作为备选
if exist "%ProgramFiles%\7-Zip\7z.exe" (
    set "sevenzip=%ProgramFiles%\7-Zip\7z.exe"
) else if exist "%ProgramFiles(x86)%\7-Zip\7z.exe" (
    set "sevenzip=%ProgramFiles(x86)%\7-Zip\7z.exe"
)

:: 显示找到的解压软件
if not "!winrar!"=="" (
    echo 使用解压软件: WinRAR
) else if not "!sevenzip!"=="" (
    echo 使用解压软件: 7-Zip
) else (
    echo 警告: 未找到WinRAR或7-Zip，将仅支持ZIP文件解压
)
echo.

:: 计数器
set /a total_files=0
set /a success_count=0
set /a error_count=0

echo 开始扫描压缩文件...
echo.

:: 遍历当前目录及所有子目录中的压缩文件
for /r %%i in (%extensions%) do (
    if exist "%%i" (
        set /a total_files+=1
        echo 正在处理: %%~nxi
        
        :: 获取文件路径和名称（不含扩展名）
        set "filepath=%%~dpi"
        set "filename=%%~ni"
        set "fullpath=%%i"
        
        :: 创建解压目录（以文件名命名）
        set "extract_dir=!filepath!!filename!"
        
        :: 如果目录已存在，询问是否覆盖
        if exist "!extract_dir!" (
            echo   目录 "!filename!" 已存在
            set /p choice="  是否覆盖？(Y/N): "
            if /i "!choice!" neq "Y" (
                echo   跳过文件: %%~nxi
                echo.
                goto :continue
            )
            rd /s /q "!extract_dir!" 2>nul
        )
        
        :: 创建解压目录
        mkdir "!extract_dir!" 2>nul
        
        :: 根据文件扩展名选择解压方法
        set "file_ext=%%~xi"
        set "extract_success=0"

        if /i "!file_ext!"==".zip" (
            if not "!winrar!"=="" (
                call :extract_winrar "!fullpath!" "!extract_dir!"
            ) else (
                call :extract_zip "!fullpath!" "!extract_dir!"
            )
        ) else if not "!winrar!"=="" (
            call :extract_winrar "!fullpath!" "!extract_dir!"
        ) else if not "!sevenzip!"=="" (
            call :extract_7zip "!fullpath!" "!extract_dir!"
        ) else (
            echo   错误: 不支持的文件格式 !file_ext! 且未安装解压软件
            set /a error_count+=1
            goto :continue
        )
        
        if !extract_success!==1 (
            echo   ✓ 解压成功: !filename!
            set /a success_count+=1
        ) else (
            echo   ✗ 解压失败: !filename!
            set /a error_count+=1
            :: 删除可能创建的空目录
            rd "!extract_dir!" 2>nul
        )
        
        :continue
        echo.
    )
)

:: 显示统计结果
echo ========================================
echo           解压完成统计
echo ========================================
echo 总文件数: %total_files%
echo 成功解压: %success_count%
echo 解压失败: %error_count%
echo ========================================

if %total_files%==0 (
    echo 未找到任何压缩文件！
)

echo.
pause
goto :eof

:: ZIP文件解压函数（使用PowerShell）
:extract_zip
set "zip_file=%~1"
set "dest_dir=%~2"

powershell -command "try { Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%zip_file%', '%dest_dir%'); exit 0 } catch { exit 1 }" >nul 2>&1

if %errorlevel%==0 (
    set "extract_success=1"
) else (
    set "extract_success=0"
)
goto :eof

:: 7-Zip解压函数
:extract_7zip
set "archive_file=%~1"
set "dest_dir=%~2"

"%sevenzip%" x "%archive_file%" -o"%dest_dir%" -y >nul 2>&1

if %errorlevel%==0 (
    set "extract_success=1"
) else (
    set "extract_success=0"
)
goto :eof
